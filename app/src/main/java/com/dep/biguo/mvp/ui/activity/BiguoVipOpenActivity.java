package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.animation.ValueAnimator;
import android.os.Bundle;
import android.text.Html;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.hjq.toast.ToastUtils;
import android.view.View;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.biguo.utils.dialog.MessageDialog;
import com.jess.arms.base.BaseActivity;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.BiguoVipOpenBean;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.TableRowData;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.BiguoVipOpenActivityBinding;
import com.dep.biguo.di.component.DaggerBiguoVipOpenComponent;
import com.dep.biguo.dialog.RuleDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.BiguoVipOpenContract;
import com.dep.biguo.mvp.presenter.BiguoVipOpenPresenter;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.adapter.VipTableAdapter;
import com.dep.biguo.mvp.ui.adapter.VipTableSimpleAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.util.VipTableDataProvider;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class BiguoVipOpenActivity extends BaseActivity<BiguoVipOpenPresenter> implements BiguoVipOpenContract.View,View.OnClickListener {
    private BiguoVipOpenActivityBinding binding;
    private NormalToolbarUtil toolbarUtil;


    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerBiguoVipOpenComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_open_activity);

        // Set up click listeners manually
        binding.openGroupVipLayout.setOnClickListener(this);
        binding.serviceAgreementView.setOnClickListener(this);
        binding.ruleView.setOnClickListener(this);
        binding.vipInfoLayout.setOnClickListener(this);
        binding.discountInfoLayout.setOnClickListener(this);
        binding.singlePurchaseCard.setOnClickListener(this);
        binding.groupPurchaseCard.setOnClickListener(this);

        // Set up VIP table RecyclerView
        setupVipTable();

        toolbarUtil = new NormalToolbarUtil(this)
                .setLeftDrawableRes(R.drawable.arrow_white_back)
                .setMustDay(this, true)
                .setAnchorView(binding.ruleView)
                .setCenterTextColor(Color.WHITE);
        toolbarUtil.setFollowScrollListener(binding.nestedScrollView, (toolbar, changeRate, isDay) -> {
                    //计算图标的颜色，深色模式,图标只需要白色
                    int iconRgb = Math.max(102, 255 - (int) ((changeRate) * 255));
                    int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

                    //计算文字的颜色，深色模式,文字只需要白色 
                    int textRgb = Math.max(51, 255 - (int) ((changeRate) * 255));
                    int textColor = Color.argb(255, textRgb, textRgb, textRgb);

                    setIconAndTextAndBackgroundColor(iconColor, textColor);
                });

        String text = "我已阅读并同意<font color='#FE5656'>会员协议</font>，且知晓权益为一年有效期";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.serviceAgreementView.setText(Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY));
        } else {
            binding.serviceAgreementView.setText(Html.fromHtml(text));
        }

        updateCardSelection(true);

        // 强制确保删除线在运行时生效（有些系统版本对 xml 的 paintFlags 兼容性不一致）
        TextView singleOriginalPrice = binding.getRoot().findViewById(R.id.singleOriginalPrice);
        TextView groupOriginalPrice = binding.getRoot().findViewById(R.id.groupOriginalPrice);
        if (singleOriginalPrice != null) {
            singleOriginalPrice.setPaintFlags(singleOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }
        if (groupOriginalPrice != null) {
            groupOriginalPrice.setPaintFlags(groupOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }

        // 点击变色：content_area
        View contentArea = binding.getRoot().findViewById(R.id.content_area);
        TextView danDuGouMai = binding.getRoot().findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
        TextView pMoney = binding.getRoot().findViewById(R.id.p_money);
        if (contentArea != null) {
            contentArea.setOnClickListener(v -> {
                int color = Color.parseColor("#FE5656");
                if (danDuGouMai != null) danDuGouMai.setTextColor(color);
                if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(color);
                if (pMoney != null) pMoney.setTextColor(color);

                // 增加宽度10dp
                ViewGroup.LayoutParams layoutParams = contentArea.getLayoutParams();
                if (layoutParams != null) {
                    // 将10dp转换为像素
                    int widthIncreasePx = (int) (10 * getResources().getDisplayMetrics().density);
                    layoutParams.width = layoutParams.width == ViewGroup.LayoutParams.MATCH_PARENT ?
                        contentArea.getWidth() + widthIncreasePx : layoutParams.width + widthIncreasePx;
                    contentArea.setLayoutParams(layoutParams);
                }
            });
        }

        // 点击变色：group_purchase_content_area
        View groupPurchaseContentArea = binding.getRoot().findViewById(R.id.group_purchase_content_area);
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);
        if (groupPurchaseContentArea != null) {
            // 默认高亮（已在 XML 设置为 #FE5656）
            int defaultHighlight = Color.parseColor("#FE5656");
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(defaultHighlight);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(defaultHighlight);
            if (ppMoney != null) ppMoney.setTextColor(Color.parseColor("#333333"));

            groupPurchaseContentArea.setOnClickListener(v -> {
                int color = Color.parseColor("#FE5656");
                // 点击时改变
                if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(color);
                if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(color);
                if (ppMoney != null) ppMoney.setTextColor(Color.parseColor("#333333"));

                // 减少宽度10dp
                ViewGroup.LayoutParams layoutParams = groupPurchaseContentArea.getLayoutParams();
                if (layoutParams != null) {
                    // 将10dp转换为像素
                    int widthDecreasePx = (int) (10 * getResources().getDisplayMetrics().density);
                    layoutParams.width = layoutParams.width == ViewGroup.LayoutParams.MATCH_PARENT ?
                        groupPurchaseContentArea.getWidth() - widthDecreasePx : layoutParams.width - widthDecreasePx;
                    groupPurchaseContentArea.setLayoutParams(layoutParams);
                }
            });
        }

        // 当离开页面时恢复（示例：onPause 恢复，可根据你的需求选择 onStop/onDestroy）


        return 0;
    }

    private void setupVipTable() {
        androidx.recyclerview.widget.RecyclerView rvPrivilege = binding.getRoot().findViewById(R.id.recycler_privilege);
        androidx.recyclerview.widget.RecyclerView rvSuper = binding.getRoot().findViewById(R.id.recycler_super_vip);
        androidx.recyclerview.widget.RecyclerView rvVip = binding.getRoot().findViewById(R.id.recycler_vip);
        androidx.recyclerview.widget.RecyclerView rvRegular = binding.getRoot().findViewById(R.id.recycler_regular);

        rvPrivilege.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvSuper.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvVip.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvRegular.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));

        rvPrivilege.setAdapter(new VipTableSimpleAdapter(mapToPrivilegeItems(VipTableDataProvider.providePrivilegeList())));
        rvSuper.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideSuperVipList()));
        rvVip.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideVipList()));
        rvRegular.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideRegularList()));
    }

    private List<VipTableSimpleAdapter.CellItem> mapToPrivilegeItems(List<String> titles){
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        for (String t: titles){
            list.add(new VipTableSimpleAdapter.CellItem(t, Color.parseColor("#333333")));
        }
        return list;
    }

    private void addCellToColumn(LinearLayout column, String text, TableRowData.RowType rowType, boolean isHeader, int columnType) {
        TextView tv = new TextView(this);
        tv.setText(text);
        tv.setGravity(Gravity.CENTER);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));
        tv.setLayoutParams(params);
        
        // 只为VIP和普通用户的标题行设置背景
        // columnType: 0=privilege, 1=super vip, 2=vip, 3=regular
        if (isHeader && columnType == 2) {
            // VIP列标题 - 粉红色背景
            tv.setBackgroundResource(R.drawable.bg_vip_column_red);
        } else if (isHeader && columnType == 3) {
            // 普通用户列标题 - 灰色背景
            tv.setBackgroundResource(R.drawable.bg_regular_column_gray);
        } else {
            tv.setBackgroundColor(Color.TRANSPARENT);
        }

        // 根据列类型设置文本颜色
        // columnType: 0=privilege, 1=super vip (yellow), 2=vip (red), 3=regular (gray)
        if (isHeader) {
            tv.setTextSize(14);
            tv.setTypeface(null, Typeface.BOLD);
            switch(columnType) {
                case 1: // Super VIP - golden/yellow text
                    tv.setTextColor(Color.parseColor("#AD8A49"));
                    break;
                case 2: // VIP - 白色文字（因为有红色背景）
                    tv.setTextColor(Color.WHITE);
                    break;
                default: // Privilege and Regular - dark text
                    tv.setTextColor(Color.parseColor("#333333"));
                    break;
            }
        } else {
            tv.setTextSize(12);
            switch(columnType) {
                case 1: // Super VIP - golden/yellow text
                    tv.setTextColor(Color.parseColor("#AD8A49"));
                    break;
                default: // 其他列 - gray text
                    tv.setTextColor(Color.parseColor("#666666"));
                    break;
            }
        }

        column.addView(tv);
    }

    private void addCellToColumn(LinearLayout column, String text, int iconRes, TableRowData.CellType cellType, TableRowData.RowType rowType, boolean isHeader, int columnType) {
        View cellView;
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));


        if (cellType == TableRowData.CellType.TEXT) {
            TextView tv = new TextView(this);
            tv.setText(text);
            tv.setGravity(Gravity.CENTER);
            tv.setLayoutParams(params);

            // 根据列类型设置文本颜色
            // columnType: 0=privilege, 1=super vip (yellow), 2=vip (red), 3=regular (gray)
            if (isHeader) {
                tv.setTextSize(14);
                tv.setTypeface(null, Typeface.BOLD);
                switch(columnType) {
                    case 1: // Super VIP - golden/yellow text
                        tv.setTextColor(Color.parseColor("#AD8A49"));
                        break;
                    case 2: // VIP - 白色文字（因为有红色背景）
                        tv.setTextColor(Color.WHITE);
                        break;
                    default: // Privilege and Regular - dark text
                        tv.setTextColor(Color.parseColor("#333333"));
                        break;
                }
            } else {
                tv.setTextSize(12);
                switch(columnType) {
                    case 1: // Super VIP - golden/yellow text
                        tv.setTextColor(Color.parseColor("#AD8A49"));
                        break;
                    default: // 其他列 - gray text
                        tv.setTextColor(Color.parseColor("#666666"));
                        break;
                }
            }
            cellView = tv;
        } else {
            LinearLayout container = new LinearLayout(this);
            container.setGravity(Gravity.CENTER);
            container.setLayoutParams(params);
            
            ImageView iv = new ImageView(this);
            iv.setImageResource(iconRes);
            LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(20), dpToPx(20));
            iv.setLayoutParams(iconParams);
            
            container.addView(iv);
            cellView = container;
        }

        // 只为VIP和普通用户的标题行设置背景
        // columnType: 0=privilege, 1=super vip, 2=vip, 3=regular
        if (isHeader && columnType == 2) {
            // VIP列标题 - 粉红色背景
            cellView.setBackgroundResource(R.drawable.bg_vip_column_red);
        } else if (isHeader && columnType == 3) {
            // 普通用户列标题 - 灰色背景
            cellView.setBackgroundResource(R.drawable.bg_regular_column_gray);
        } else {
            cellView.setBackgroundColor(Color.TRANSPARENT);
        }
        column.addView(cellView);
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**给标题栏的图标和文字着色
     * @param iconColor       图标颜色
     * @param rightTextColor  标题文字颜色
     */
    public void setIconAndTextAndBackgroundColor(int iconColor, int rightTextColor){
        toolbarUtil.setIconAndTextColor(iconColor, rightTextColor, Color.WHITE);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        if (mPresenter != null) {
            mPresenter.getData(true);
        }
    }

    public void onClick(View view){
        if(view.getId() == R.id.openGroupVipLayout){
            if(!MainAppUtils.checkLogin(BiguoVipOpenActivity.this)) return;
            BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
            String content = String.format(Locale.CHINA, "邀请1名新用户参团享半价开通折扣卡，开通后可享%.1f折优惠;24小时内未成团自动退款!", Float.parseFloat(bean.getDiscount()) * 10);

            if(mPresenter.getBiguoVipBean().getGroup_info() == null){
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent(content)
                        .setNegativeText("关闭")
                        .setPositiveText("我要购买")
                        .setPositiveClickListener(v -> mPresenter.getDiscountCard(1))
                        .builder()
                        .show();
            }else {
                BiguoVipOpenBean.GroupInfo groupInfo = bean.getGroup_info();
                String shareUrl = groupInfo.getShare_url();
                if(!shareUrl.contains("?")){
                    shareUrl += "?";
                }
                shareUrl = shareUrl
                        + (shareUrl.endsWith("?") ? "" : "&")
                        + "group_id=" + groupInfo.getGroup_id()
                        + "&users_id=" + groupInfo.getUsers_id()
                        + "&inviter_id=" + groupInfo.getUsers_id();
                new ShareDialog.Builder(getActivity())
                        .setShareTitle("您有一个好友拼团福利待领取")
                        .setShareContent("新用户注册并加入拼团，立享半价解锁笔果折扣卡！")
                        .setShareUrl(shareUrl)
                        .setOnShareListener((type) -> showMessage("分享成功，请等待被邀请用户注册并参与拼团"))
                        .builder()
                        .show();
            }

        }else if(view.getId() == R.id.serviceAgreementView){
            // 不手动 toggle，避免与 CheckBox 默认点击切换重复，导致状态被“反复切换回去”
            HtmlActivity.start(this, Constant.AGREEMENT_USER3);

        }else if(view.getId() == R.id.ruleView){
            mPresenter.getRule("membership_rule");
        } else if (view.getId() == R.id.vip_info_layout) {
            updateCardSelection(true);
        } else if (view.getId() == R.id.discount_info_layout) {
            updateCardSelection(false);
            // 点击折扣信息时更新价格显示
            TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setText("7.9");
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setText("4.9");
        } else if (view.getId() == R.id.singlePurchaseCard) {
            updatePurchaseCardWeight(true);
        } else if (view.getId() == R.id.groupPurchaseCard) {
            updatePurchaseCardWeight(false);
        }
    }

    @Override
    public void setDataSuccess(BiguoVipOpenBean bean) {
        //如果列表是空的，则创建一个数组
        if(AppUtil.isEmpty(bean.getEnjoy_discounts())){
            bean.setEnjoy_discounts(new ArrayList<>());
        }
        if(AppUtil.isEmpty(bean.getNot_enjoy_discounts())){
            bean.setNot_enjoy_discounts(new ArrayList<>());
        }

        // 显示价格和开通年限
        binding.priceAndValidityView.setText("AI功能畅享用");
        // 显示折扣信息
        try {
            binding.discountView.setText("折扣会员卡");
        }catch (Exception e){
            binding.discountView.setText("专享折扣价");
        }
        // 显示享受的权益数量
        binding.enjoyCountView.setText("课程折扣8.8折");

        // 处理拼团相关显示
        if(bean.getGroup_info() == null){
            binding.openGroupVipView.setText(String.format("¥%s 立即邀请", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            binding.countDownTimeView.setVisibility(View.GONE);
        }else {
            binding.openGroupVipView.setText(String.format("¥%s 拼团中", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            binding.countDownTimeView.setVisibility(View.VISIBLE);
        }
        binding.openCountView.setText(String.format("超级折扣会员卡享受%s大权益", 8));

    }

    public int getDiscountsCount(List<BiguoVipOpenBean.DiscountsBean> list){
        int count = 0;
        for (int i=0;i<list.size(); i++){
            if(!AppUtil.isEmpty(list.get(i).getTitle())) {
                count ++;
            }
        }
        return count;
    }

    @Override
    public void refreshCountTime(long countTime) {
        binding.countDownTimeView.setText(TimeFormatUtils.formatMillisecond(countTime));
    }

    @Override
    public void showPayDialog(int isGroup, List<DiscountBean> allDiscount) {
        BiguoVipOpenBean openBean = mPresenter.getBiguoVipBean();
        String payPrice = isGroup == StartFinal.YES ? openBean.getGroup_price() : openBean.getPrice();
        new DiscountPayDialog.Builder(this, PayUtils.MEMBERSHIP)
                .setGoodsName("笔果折扣卡")
                .setShowGuobi(false)
                .setPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setPayPrice(Float.parseFloat(AppUtil.isEmpty(payPrice, "9999")))
                .setDiscountList(allDiscount)
                .setOnPayListener((joinGroup, discount, payType) -> {
                    int coupon_id = discount == null ? 0 : discount.getId();
                    mPresenter.payOrder(payType, 0, coupon_id, isGroup, openBean.getGroup_id());
                })
                .build()
                .show();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Override
    public void paySuccess() {
        showMessage("支付成功");
        UserBean userBean = UserCache.getUserCache();
        userBean.setMembership(1);
        UserCache.cacheUser(userBean);
        ArmsUtils.startActivity(BiguoVipActivity.class);
        finish();
    }

    @Override
    public void getRuleSuccess(List<String> rule) {
        new RuleDialog(this)
                .setTitleText("购买须知")
                .setRules(rule)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mPresenter !=null && mPresenter.getBiguoVipBean() != null) {
            mPresenter.getData(false);
        }

        // 恢复默认颜色：拼团标题与金额为高亮色，货币符号恢复为深色
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);
        if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(Color.parseColor("#FE5656"));
        if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(Color.parseColor("#FE5656"));
        if (ppMoney != null) ppMoney.setTextColor(Color.parseColor("#333333"));
    }

    @Override
    public void showLoading() {
        // TODO: 实现加载中的UI，例如显示一个ProgressBar
    }

    @Override
    public void hideLoading() {
        // TODO: 隐藏加载中的UI
    }
    public void setToolBarErrorStyle(boolean isSuccess){
        if(isSuccess){
            setIconAndTextAndBackgroundColor(Color.WHITE, Color.WHITE);
        }else {
            setIconAndTextAndBackgroundColor(AppUtil.getColorRes(this, R.color.tblack), AppUtil.getColorRes(this, R.color.tblack));
        }
    }

    private void updateCardSelection(boolean isSuperVipSelected) {
        if (isSuperVipSelected) {
            // 选中超级会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#FFE9A3"));
            binding.priceAndValidityView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.discountView.setTextColor(Color.parseColor("#FFFFFF"));
            binding.enjoyCountView.setTextColor(Color.parseColor("#FFFFFF"));

            binding.leftGrainSuper.setVisibility(View.VISIBLE);
            binding.rightGrainSuper.setVisibility(View.VISIBLE);
            binding.triangleSuper.setVisibility(View.VISIBLE);

            binding.leftGrainDiscount.setVisibility(View.INVISIBLE);
            binding.rightGrainDiscount.setVisibility(View.INVISIBLE);
            binding.triangleDiscount.setVisibility(View.INVISIBLE);



        } else {
            // 选中折扣会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#FFFFFF"));
            binding.priceAndValidityView.setTextColor(Color.parseColor("#FFFFFF"));
            binding.discountView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.enjoyCountView.setTextColor(Color.parseColor("#FFE9A3"));

            binding.leftGrainSuper.setVisibility(View.INVISIBLE);
            binding.rightGrainSuper.setVisibility(View.INVISIBLE);
            binding.triangleSuper.setVisibility(View.INVISIBLE);

            binding.leftGrainDiscount.setVisibility(View.VISIBLE);
            binding.rightGrainDiscount.setVisibility(View.VISIBLE);
            binding.triangleDiscount.setVisibility(View.VISIBLE);


        }
        // 同步更新下方表格的列头背景：
        // isSuperVipSelected=true 显示黄色（超级会员）列头，隐藏 VIP 红色列头
        // isSuperVipSelected=false 隐藏黄色列头，显示 VIP 红色列头
        updateTableHeaderBackground(isSuperVipSelected);
        updatePurchaseCardWeight(true);
    }

    private void updateTableHeaderBackground(boolean isSuperVipSelected) {
        LinearLayout superVipColumn = binding.superVipColumn; // 自身列容器（含上下边框 bg_table_border_top_bottom）
        LinearLayout vipColumn = binding.vipColumn;           // 自身列容器（含上下边框 bg_table_border_top_bottom）

        // 顶部表头背景
        View superHeader = null;
        View vipHeader = null;
        if (superVipColumn != null && superVipColumn.getChildCount() > 0) {
            superHeader = superVipColumn.getChildAt(0);
        }
        if (vipColumn != null && vipColumn.getChildCount() > 0) {
            vipHeader = vipColumn.getChildAt(0);
        }

        // 叠加的背景遮罩视图（整列底色）：黄色与红色
        View yellowBackground = binding.getRoot().findViewById(R.id.background_yellow_column);
        View redBackground = binding.getRoot().findViewById(R.id.background_red_column);

        if (isSuperVipSelected) {
            // 点击 vip_info_layout：
            // 1) super_vip_column 的上下边框隐藏
            if (superVipColumn != null) superVipColumn.setBackgroundColor(Color.TRANSPARENT);
            // 2) super 表头黄色隐藏
            if (superHeader != null) superHeader.setBackgroundColor(Color.TRANSPARENT);
            // 3) 显示黄色遮罩，隐藏红色遮罩
            if (yellowBackground != null) yellowBackground.setVisibility(View.VISIBLE);
            if (redBackground != null) redBackground.setVisibility(View.GONE);
            // 4) 保持 VIP 列容器边框与表头可见
            if (vipColumn != null) vipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);
        } else {
            // 点击 discount_info_layout：
            // 1) 隐藏黄色遮罩，显示红色遮罩
            if (yellowBackground != null) yellowBackground.setVisibility(View.GONE);
            if (redBackground != null) redBackground.setVisibility(View.VISIBLE);
            // 2) 隐藏 VIP 列容器的上下边框
            if (vipColumn != null) vipColumn.setBackgroundColor(Color.TRANSPARENT);
            // 3) 保持 VIP 表头红色可见
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);
            // 4) 保持 super_vip_column 的上下边框可见，表头可按需显示柔黄
            if (superVipColumn != null) superVipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (superHeader != null) superHeader.setBackgroundResource(R.drawable.bg_super_vip_header);
        }
    }

    private void updatePurchaseCardWeight(boolean isSinglePurchaseSelected) {
        final float targetWeight = 1.2f;
        final float normalWeight = 1.0f;
        final int duration = 300; // 动画时长

        LinearLayout.LayoutParams singleParams = (LinearLayout.LayoutParams) binding.singlePurchaseCard.getLayoutParams();
        LinearLayout.LayoutParams groupParams = (LinearLayout.LayoutParams) binding.groupPurchaseCard.getLayoutParams();

        float singleStartWeight = singleParams.weight;
        float groupStartWeight = groupParams.weight;

        float singleEndWeight = isSinglePurchaseSelected ? targetWeight : normalWeight;
        float groupEndWeight = isSinglePurchaseSelected ? normalWeight : targetWeight;

        // 为 singlePurchaseCard 创建动画
        ValueAnimator singleAnimator = ValueAnimator.ofFloat(singleStartWeight, singleEndWeight);
        singleAnimator.addUpdateListener(animation -> {
            singleParams.weight = (float) animation.getAnimatedValue();
            binding.singlePurchaseCard.setLayoutParams(singleParams);
        });
        singleAnimator.setDuration(duration);
        singleAnimator.start();

        // 为 groupPurchaseCard 创建动画
        ValueAnimator groupAnimator = ValueAnimator.ofFloat(groupStartWeight, groupEndWeight);
        groupAnimator.addUpdateListener(animation -> {
            groupParams.weight = (float) animation.getAnimatedValue();
            binding.groupPurchaseCard.setLayoutParams(groupParams);
        });
        groupAnimator.setDuration(duration);
        groupAnimator.start();

        // 获取相关的TextView
        TextView danDuGouMai = binding.getRoot().findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
        TextView pMoney = binding.getRoot().findViewById(R.id.p_money);
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);

        if (isSinglePurchaseSelected) {
            // 单独购买被选中
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);

            // 设置单独购买的文字颜色为高亮色
            int highlightColor = Color.parseColor("#FE5656");
            int normalColor = Color.parseColor("#333333");
            if (danDuGouMai != null) danDuGouMai.setTextColor(highlightColor);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(highlightColor);
            if (pMoney != null) pMoney.setTextColor(highlightColor);

            // 恢复拼团购买的文字颜色为默认色
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(highlightColor);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(highlightColor);
            if (ppMoney != null) ppMoney.setTextColor(normalColor);
        } else {
            // 拼团购买被选中
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);

            // 恢复单独购买的文字颜色为默认色
            int normalColor = Color.parseColor("#333333");
            int highlightColor = Color.parseColor("#FE5656");
            if (danDuGouMai != null) danDuGouMai.setTextColor(normalColor);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(normalColor);
            if (pMoney != null) pMoney.setTextColor(normalColor);

            // 设置拼团购买的文字颜色为高亮色
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(highlightColor);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(highlightColor);
            if (ppMoney != null) ppMoney.setTextColor(normalColor);
        }
    }
}
